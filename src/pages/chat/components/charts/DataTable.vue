<template>
  <view class="data-table">
    <view v-if="title" class="table-title">{{ title }}</view>
    <view class="table-container">
      <scroll-view scroll-x class="table-scroll">
        <view class="table-content">
          <!-- 表头 -->
          <view class="table-header">
            <view 
              v-for="(column, index) in columns" 
              :key="index" 
              class="table-cell header-cell"
              :style="{ width: getColumnWidth(column) }"
            >
              <text class="cell-text">{{ getColumnLabel(column) }}</text>
            </view>
          </view>
          
          <!-- 表格数据 -->
          <view class="table-body">
            <view 
              v-for="(row, rowIndex) in displayData" 
              :key="rowIndex" 
              class="table-row"
              :class="{ 'even-row': rowIndex % 2 === 1 }"
            >
              <view 
                v-for="(column, colIndex) in columns" 
                :key="colIndex" 
                class="table-cell body-cell"
                :style="{ width: getColumnWidth(column) }"
              >
                <text class="cell-text">{{ formatCellValue(row[column]) }}</text>
              </view>
            </view>
          </view>
          
          <!-- 显示更多数据提示 -->
          <view v-if="hasMoreData" class="more-data-tip">
            <text class="tip-text">还有 {{ data.length - maxDisplayRows }} 行数据...</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  data: Record<string, any>[]
  title?: string
  columnLabels?: Record<string, string>
  maxDisplayRows?: number
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  columnLabels: () => ({}),
  maxDisplayRows: 10
})

// 获取表格列
const columns = computed(() => {
  if (!props.data || props.data.length === 0) return []
  return Object.keys(props.data[0])
})

// 显示的数据（限制行数）
const displayData = computed(() => {
  if (!props.data) return []
  return props.data.slice(0, props.maxDisplayRows)
})

// 是否有更多数据
const hasMoreData = computed(() => {
  return props.data && props.data.length > props.maxDisplayRows
})

// 获取列标签
const getColumnLabel = (column: string) => {
  if (props.columnLabels && props.columnLabels[column]) {
    return props.columnLabels[column]
  }
  
  // 默认标签映射
  const defaultLabels: Record<string, string> = {
    'label': '名称',
    'value': '数值',
    'name': '名称',
    'count': '数量',
    'amount': '金额',
    'date': '日期',
    'time': '时间'
  }
  
  return defaultLabels[column] || column
}

// 获取列宽度
const getColumnWidth = (column: string) => {
  // 根据列名或内容长度动态设置宽度
  const minWidth = 120
  const maxWidth = 200
  
  // 可以根据列名设置特定宽度
  const columnWidths: Record<string, number> = {
    'id': 80,
    'name': 150,
    'label': 150,
    'value': 100,
    'count': 100,
    'amount': 120,
    'date': 140,
    'time': 140
  }
  
  const width = columnWidths[column] || minWidth
  return `${Math.min(Math.max(width, minWidth), maxWidth)}rpx`
}

// 格式化单元格值
const formatCellValue = (value: any) => {
  if (value === null || value === undefined) {
    return '-'
  }
  
  if (typeof value === 'number') {
    // 如果是数字，保留合适的小数位数
    if (Number.isInteger(value)) {
      return value.toString()
    } else {
      return value.toFixed(2)
    }
  }
  
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  
  return String(value)
}
</script>

<style scoped lang="scss">
.data-table {
  width: 100%;
  background-color: #ffffff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin: 16rpx 24rpx; // 添加外边距，避免贴边
}

.table-title {
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e4e7ed;
}

.table-container {
  width: 100%;
  overflow: hidden;
}

.table-scroll {
  width: 100%;
  white-space: nowrap;
}

.table-content {
  display: inline-block;
  min-width: 100%;
}

.table-header {
  display: flex;
  background-color: #f1f3f4;
  border-bottom: 2rpx solid #e4e7ed;
}

.table-body {
  background-color: #ffffff;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid rgba(228, 231, 237, 0.5);
  
  &.even-row {
    background-color: #fafafa;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.table-cell {
  display: flex;
  align-items: center;
  padding: 24rpx 16rpx;
  border-right: 1rpx solid rgba(228, 231, 237, 0.3);
  
  &:last-child {
    border-right: none;
  }
}

.header-cell {
  background-color: #f1f3f4;
  
  .cell-text {
    font-size: 26rpx;
    font-weight: 600;
    color: #333;
  }
}

.body-cell {
  .cell-text {
    font-size: 24rpx;
    color: #333;
    line-height: 1.4;
  }
}

.cell-text {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.more-data-tip {
  padding: 32rpx;
  text-align: center;
  background-color: #f8f9fa;
  border-top: 1rpx solid #e4e7ed;
  
  .tip-text {
    font-size: 24rpx;
    color: #666;
  }
}
</style>
