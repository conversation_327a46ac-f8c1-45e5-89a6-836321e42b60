<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useChatStore } from '@/store/chatStore'
import { useUserStore } from '@/store/user'
import ChatNavbar from './components/ChatNavbar.vue'
import MessageList from './components/MessageList.vue'
import HistoryDrawer from './components/HistoryDrawer.vue'
import BottomPanel from './components/BottomPanel.vue'
import LoadingMask from './components/LoadingMask.vue'

// 常量定义
const SCROLL_TO_BOTTOM = 999999
const MESSAGES = {
  LOGIN_REQUIRED: '请先登录',
  INIT_FAILED: '初始化失败，请重试',
  SEND_FAILED: '发送失败',
  LOAD_SUCCESS: '已加载历史对话',
  LOAD_FAILED: '加载失败',
  SWITCH_FAILED: '切换失败',
  NEW_CHAT_SUCCESS: '已开启新对话',
  NEW_CHAT_CONFIRM: '确定要开启新对话吗？当前对话记录将被清除。'
}

const chatStore = useChatStore()
const userStore = useUserStore()

// 响应式数据
const scrollTop = ref(0)
const showHistory = ref(false)
const statusBarHeight = ref(0)
const safeAreaBottom = ref(0)

// 计算属性 - 简化为必要的属性
const messages = computed(() => chatStore.messages)
const currentKnowledgeName = computed(() => chatStore.currentKnowledgeName)

// 统一的消息提示函数
const showToast = (title: string, icon: 'success' | 'error' | 'none' = 'none') => {
  uni.showToast({ title, icon })
}

// 获取系统信息
onMounted(async () => {
  initSystemInfo()

  if (!checkLoginStatus()) return

  await initializeChat()
})

// 初始化系统信息
const initSystemInfo = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  safeAreaBottom.value = systemInfo.safeAreaInsets?.bottom || 0
}

// 检查登录状态
const checkLoginStatus = () => {
  if (!userStore.isLoggedIn) {
    showToast(MESSAGES.LOGIN_REQUIRED)
    uni.reLaunch({ url: '/pages/login/index' })
    return false
  }
  return true
}

// 初始化聊天
const initializeChat = async () => {
  try {
    await chatStore.initChat()
  } catch (error) {
    console.error('初始化聊天失败:', error)
    showToast(MESSAGES.INIT_FAILED)
  }
}

// 监听消息变化，自动滚动到底部
watch(messages, () => {
  nextTick(scrollToBottom)
}, { deep: true })

// 滚动到底部
const scrollToBottom = () => {
  scrollTop.value = SCROLL_TO_BOTTOM
}

// 发送消息
const sendMessage = async (text: string) => {
  if (!text.trim() || chatStore.isSending) return

  try {
    await chatStore.sendMessage(text)
  } catch (error) {
    showToast(error.message || MESSAGES.SEND_FAILED)
  }
}

// 停止聊天
const stopChat = () => {
  chatStore.stopChat()
  showToast('已停止生成', 'success')
}

// 历史列表显示控制
const showHistoryList = () => { showHistory.value = true }
const hideHistoryList = () => { showHistory.value = false }

// 选择历史对话
const selectHistory = async (history: any) => {
  try {
    await chatStore.loadHistoryChat(history.id)
    hideHistoryList()
    showToast(MESSAGES.LOAD_SUCCESS, 'success')
  } catch (error) {
    showToast(MESSAGES.LOAD_FAILED)
  }
}

// 选择知识库
const selectKnowledge = async (knowledge: any) => {
  if (chatStore.currentKnowledge?.label === knowledge.label) return

  try {
    await chatStore.switchKnowledge(knowledge)
    showToast(`已切换到 ${knowledge.label}`, 'success')
  } catch (error) {
    showToast(MESSAGES.SWITCH_FAILED)
  }
}

// 测试图表功能
const testChart = () => {
  chatStore.testChart()
  showToast('已添加测试图表', 'success')
}

// 开启新对话
const startNewChat = () => {
  uni.showModal({
    title: '确认操作',
    content: MESSAGES.NEW_CHAT_CONFIRM,
    success: (res) => {
      if (res.confirm) {
        chatStore.startNewChat()
        showToast(MESSAGES.NEW_CHAT_SUCCESS, 'success')
      }
    }
  })
}
</script>

<template>
  <view class="chat-container">
    <!-- 自定义导航栏 -->
    <ChatNavbar
      :status-bar-height="statusBarHeight"
      :title="currentKnowledgeName || '智能对话'"
      @show-history="showHistoryList"
      @new-chat="startNewChat"
      @test-chart="testChart"
    />

    <!-- 消息列表 -->
    <MessageList
      :messages="messages"
      :scroll-top="scrollTop"
      :current-knowledge-name="currentKnowledgeName"
      :common-questions="chatStore.commonQuestions"
      :questions-loading="chatStore.isLoading"
      @select-question="sendMessage"
    />

    <!-- 底部面板：知识库选项卡 + 输入框 -->
    <BottomPanel
      :knowledge-list="chatStore.knowledgeList"
      :current-knowledge="chatStore.currentKnowledge"
      :is-sending="chatStore.isSending"
      :is-streaming="chatStore.isStreaming"
      :safe-area-bottom="safeAreaBottom"
      @select-knowledge="selectKnowledge"
      @send="sendMessage"
      @stop="stopChat"
    />

    <!-- 历史对话列表 -->
    <HistoryDrawer
      :show="showHistory"
      :history-list="chatStore.historyList"
      @close="hideHistoryList"
      @select-history="selectHistory"
    />

    <!-- 加载遮罩 -->
    <LoadingMask :show="chatStore.isLoading" />
  </view>
</template>

<style scoped>
page {
  background-color:#F7F8FB;
}
</style>

<style scoped lang="scss">
@import './styles/variables.scss';

.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

</style>
