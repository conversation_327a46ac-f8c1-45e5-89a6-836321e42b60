<template>
  <view class="preview-page">
    <!-- 自定义导航栏 -->
    <view class="navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="nav-left" @click="goBack">
          <text class="back-icon">←</text>
          <text class="back-text">返回</text>
        </view>
        <view class="nav-title">
          <text class="title-text">{{ fileName || '文档预览' }}</text>
        </view>
        <view class="nav-right">
          <text class="download-btn" @click="downloadFile">下载</text>
        </view>
      </view>
    </view>

    <!-- 文档预览区域 -->
    <view class="preview-container">
      <DocumentPreview
        :file-url="fileUrl"
        :file-name="fileName"
        :file-type="fileType"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import DocumentPreview from '@/components/DocumentPreview/index.vue'

const statusBarHeight = ref(0)
const fileUrl = ref('')
const fileName = ref('')
const fileType = ref('')

// 使用onLoad获取页面参数
const onLoad = (options) => {
  fileUrl.value = decodeURIComponent(options.fileUrl || '')
  fileName.value = decodeURIComponent(options.fileName || '')
  fileType.value = options.fileType || ''

  console.log('页面参数:', { fileUrl: fileUrl.value, fileName: fileName.value, fileType: fileType.value })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 下载文件
const downloadFile = () => {
  if (!fileUrl.value) {
    uni.showToast({
      title: '文件地址无效',
      icon: 'error'
    })
    return
  }

  uni.showLoading({
    title: '下载中...'
  })

  uni.downloadFile({
    url: fileUrl.value,
    success: (res) => {
      uni.hideLoading()
      if (res.statusCode === 200) {
        uni.showToast({
          title: '下载成功',
          icon: 'success'
        })
        
        // 保存到相册（如果是图片）
        if (isImageFile()) {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              uni.showToast({
                title: '已保存到相册',
                icon: 'success'
              })
            }
          })
        }
      } else {
        uni.showToast({
          title: '下载失败',
          icon: 'error'
        })
      }
    },
    fail: () => {
      uni.hideLoading()
      uni.showToast({
        title: '下载失败',
        icon: 'error'
      })
    }
  })
}

// 判断是否为图片文件
const isImageFile = () => {
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
  const ext = fileName.value.split('.').pop()?.toLowerCase() || ''
  return imageExts.includes(ext)
}

onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
})
</script>

<style scoped lang="scss">
.preview-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #000;
}

.navbar {
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1000;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}

.nav-left {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
}

.back-icon {
  font-size: 36rpx;
  margin-right: 8rpx;
}

.back-text {
  font-size: 28rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  margin: 0 20rpx;
}

.title-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-right {
  color: #fff;
}

.download-btn {
  font-size: 28rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
}

.preview-container {
  flex: 1;
  background-color: #f5f5f5;
}
</style>
